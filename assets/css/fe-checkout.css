tr.postnl-co-tr-container > td {
	padding:0px;
}

.woocommerce-shipping-methods label:has(.postnl_shipping_method_icon) {
	display: inline-flex;
	align-items: center;
}

.postnl_shipping_method_icon{
	max-height: 32px;
	width: auto;
	margin-right: 5px;
	vertical-align: middle;
	display: inline-block;
}

.postnl_checkout_container {
	border-radius:5px;
	margin:5px;
	border:1px solid #f0f0f0;
	overflow:hidden;
	font-size:14px;
}

.postnl_checkout_container.is-hidden {
    display: none;
}

.postnl_checkout_tab_list {
	margin:0px;
	padding:0px;
	list-style-type:none;
	display:flex;
	width:100%;
	flex-direction:row;
	flex-wrap:nowrap;
}

.postnl_checkout_tab_list > li {
	flex-grow:1;
	padding:0px;
	margin:0px;
}

.postnl_checkout_tab_list > li .postnl_checkout_tab {
	display:block;
	text-align:center;
	margin:0px;
	padding:10px;
	line-height:1em;
	font-size:1em;
	font-weight:700;
	cursor: pointer;
}

.postnl_checkout_tab_list > li.active .postnl_checkout_tab {
	background-color:#ed8c00;
	color:#fff;
}

.postnl_checkout_tab_list > li .postnl_checkout_tab i {
	float:right;
	font-style:normal;
}

.postnl_checkout_tab_list > li .postnl_checkout_tab:after {
	content:"";
	display:block;
	clear:both;
}

.postnl_checkout_tab_list > li input.postnl_option {
	display:none;
}

.postnl_content {
	display:none;
}

.postnl_content.active {
	display:block;
}

.postnl_content_desc {
	padding:5px 7px;
	background-color:#f0f0f0;
}

.postnl_content .postnl_list {
	margin:0px;
	padding:0px;
	list-style-type:none;
}

.postnl_content .postnl_list > li {
	padding:0px;
	margin:0px;
}

.postnl_content .postnl_list .list_title {
	background-color:#f0f0f0;
	border-top:1px solid #d0d0d0;
	border-bottom:1px solid #d0d0d0;
	line-height:1em;
	padding:8px 6px;
}

.postnl_content .postnl_list .list_title:after {
	content:"";
	display:block;
	clear:both;
}

.postnl_content .postnl_list .list_title span {
	font-size:1em;
	font-weight:700;
}

.postnl_content .postnl_list .list_title span.company {
	float:left;
}

.postnl_content .postnl_list .list_title span.distance {
	float:right;
	font-weight:400;
}

.postnl_content .postnl_list .postnl_sub_list {
	margin:0px;
	padding:0px;
	list-style-type:none;
}

.postnl_content .postnl_list .postnl_sub_list > li {
	margin:0px;
	padding:0px;
}

.postnl_content .postnl_list .postnl_sub_list > li.active {
	background-color:#d1eeff;
}

.postnl_content .postnl_list .postnl_sub_list .postnl_sub_radio_label {
	display:block;
	padding:13px 7px;
	margin:0px;
	line-height:1.2em;
	cursor: pointer;
}

.postnl_content .postnl_list .postnl_sub_list .postnl_sub_radio_label:after {
	content:"";
	display:block;
	clear:both;
}

.postnl_content .postnl_list .postnl_sub_list .postnl_sub_radio {
	margin-right:10px;
	float:left;
}

.postnl_content .postnl_list .postnl_sub_list .postnl_sub_radio_label span {
	display:block;
	overflow:hidden;
}

.postnl_content .postnl_list .postnl_sub_list .postnl_sub_radio_label span.amount,
.postnl_content .postnl_list .postnl_sub_list .postnl_sub_radio_label span.amount * {
	display:inline;
}

.postnl_content .postnl_list .postnl_sub_list .postnl_sub_radio_label i {
	float:right;
	font-style:normal;
	margin-left: 10px;
}

.postnl_content .postnl_list .postnl_sub_list .postnl_sub_radio_label i:last-child {
	margin-left: 0;
}
.postnl_checkout_container {
	position: relative;
	/* No min-height by default */
}

.postnl_checkout_container.loading {
	min-height: 200px; /* Adjust as needed */
}

.postnl-spinner-overlay {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(255, 255, 255, 0.7);
	z-index: 9999;
	transition: opacity 0.3s ease;
	opacity: 1;
	visibility: visible;
}

.postnl-spinner-overlay.hidden {
	opacity: 0;
	visibility: hidden;
}

/* Optional: Screen reader text styling */
.screen-reader-text {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border: 0;
}
