# PostNL for WooCommerce - Comprehensive QA Test Plan

## Overview
This document outlines the complete test plan for the PostNL for WooCommerce plugin that should be executed when new WooCommerce versions are released monthly. This ensures compatibility and functionality across all plugin features.

## Test Environment Setup

### Prerequisites
- [ ] Fresh WordPress installation (latest version)
- [ ] New WooCommerce version to be tested
- [ ] PostNL plugin installed and activated
- [ ] Valid PostNL API credentials configured
- [ ] Test products with various shipping classes
- [ ] Test customer accounts for different countries (NL, BE, International)

## Test Data Examples

### Netherlands (NL) Test Addresses

#### Test Address 1 - Amsterdam
```
First Name: Jan
Last Name: de Vries
Company: (leave empty)
Address Line 1: Damrak
House Number: 123
House Number Addition: A
Postal Code: 1012 JS
City: Amsterdam
Country: Netherlands
Phone: +31 20 1234567
Email: <EMAIL>
```

#### Test Address 2 - Rotterdam
```
First Name: Maria
Last Name: van der Berg
Company: Test Company BV
Address Line 1: Coolsingel
House Number: 456
House Number Addition: (leave empty)
Postal Code: 3011 AD
City: Rotterdam
Country: Netherlands
Phone: +31 10 7654321
Email: maria.<PERSON><PERSON><PERSON>@example.nl
```

#### Test Address 3 - Utrecht (Letterbox Eligible)
```
First Name: Pieter
Last Name: Jansen
Company: (leave empty)
Address Line 1: Oudegracht
House Number: 789
House Number Addition: bis
Postal Code: 3511 AB
City: Utrecht
Country: Netherlands
Phone: +31 30 9876543
Email: <EMAIL>
```

### Belgium (BE) Test Addresses

#### Test Address 1 - Brussels
```
First Name: Sophie
Last Name: Dubois
Company: (leave empty)
Address Line 1: Rue de la Loi
House Number: 200
Postal Code: 1000
City: Brussels
Country: Belgium
Phone: +32 2 1234567
Email: <EMAIL>
```

#### Test Address 2 - Antwerp
```
First Name: Marc
Last Name: Vermeulen
Company: Antwerp Trading BVBA
Address Line 1: Meir
House Number: 85
Postal Code: 2000
City: Antwerp
Country: Belgium
Phone: +32 3 7654321
Email: <EMAIL>
```

#### Test Address 3 - Ghent
```
First Name: Els
Last Name: De Smet
Company: (leave empty)
Address Line 1: Korenlei
House Number: 12
Postal Code: 9000
City: Ghent
Country: Belgium
Phone: +32 9 9876543
Email: <EMAIL>
```

### International Test Addresses

#### Test Address 1 - Germany
```
First Name: Hans
Last Name: Mueller
Company: Mueller GmbH
Address Line 1: Hauptstraße 123
Address Line 2: (leave empty)
Postal Code: 10115
City: Berlin
Country: Germany
Phone: +49 30 1234567
Email: <EMAIL>
```

#### Test Address 2 - France
```
First Name: Marie
Last Name: Dupont
Company: (leave empty)
Address Line 1: 45 Avenue des Champs-Élysées
Address Line 2: Appartement 3B
Postal Code: 75008
City: Paris
Country: France
Phone: +33 1 23456789
Email: <EMAIL>
```

#### Test Address 3 - United Kingdom
```
First Name: James
Last Name: Smith
Company: Smith & Associates Ltd
Address Line 1: 10 Downing Street
Address Line 2: (leave empty)
Postal Code: SW1A 2AA
City: London
Country: United Kingdom
Phone: +44 20 12345678
Email: <EMAIL>
```

#### Test Address 4 - United States
```
First Name: John
Last Name: Johnson
Company: (leave empty)
Address Line 1: 123 Main Street
Address Line 2: Apt 4B
Postal Code: 10001
City: New York
State: NY
Country: United States
Phone: ****** 555 0123
Email: <EMAIL>
```

### Test Products Data

#### Product 1 - Standard Package
```
Product Name: Test Product - Standard
SKU: TEST-STD-001
Weight: 1.5 kg
Dimensions: 30 x 20 x 10 cm
Price: €25.00
Shipping Class: Standard
PostNL Product Code: 3085 (Standard delivery)
```

#### Product 2 - Letterbox Item
```
Product Name: Test Product - Letterbox
SKU: TEST-LB-001
Weight: 0.2 kg
Dimensions: 23 x 16 x 2 cm
Price: €15.00
Shipping Class: Letterbox
PostNL Product Code: 3533 (Letterbox parcel)
```

#### Product 3 - Heavy Item
```
Product Name: Test Product - Heavy
SKU: TEST-HVY-001
Weight: 8.5 kg
Dimensions: 50 x 40 x 30 cm
Price: €75.00
Shipping Class: Heavy
PostNL Product Code: 3085 (Standard delivery)
```

#### Product 4 - International Item
```
Product Name: Test Product - International
SKU: TEST-INT-001
Weight: 2.0 kg
Dimensions: 35 x 25 x 15 cm
Price: €45.00
Shipping Class: International
PostNL Product Code: 4944 (GlobalPack)
Customs Value: €45.00
HS Code: 9999.99.99
Country of Origin: Netherlands
```

### Test Payment Methods
```
- Test Credit Card: 4111 1111 1111 1111 (Visa)
- Test Credit Card: 5555 5555 5555 4444 (Mastercard)
- Bank Transfer (for testing order processing)
- Cash on Delivery (if enabled)
- PayPal Sandbox Account
```

### Test Scenarios Combinations

#### Scenario 1: NL Customer - Standard Delivery
- Customer: Jan de Vries (Amsterdam)
- Product: Test Product - Standard
- Expected: Delivery days + Pickup points available
- Default Tab: Based on setting (Home Delivery/Pickup Points)

#### Scenario 2: NL Customer - Letterbox Delivery
- Customer: Pieter Jansen (Utrecht)
- Product: Test Product - Letterbox
- Expected: Letterbox delivery option available

#### Scenario 3: BE Customer - Cross-border
- Customer: Sophie Dubois (Brussels)
- Product: Test Product - Standard
- Expected: BE delivery options, pickup points available

#### Scenario 4: International Customer - Germany
- Customer: Hans Mueller (Berlin)
- Product: Test Product - International
- Expected: International shipping only, no delivery days/pickup points

#### Scenario 5: Mixed Cart - NL Customer
- Customer: Maria van der Berg (Rotterdam)
- Products: Test Product - Standard + Test Product - Letterbox
- Expected: Standard delivery options (letterbox not available for mixed cart)

### Browser Testing Matrix
Test in the following browsers:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile Safari (iOS)
- [ ] Chrome Mobile (Android)

## 1. Plugin Installation & Activation

### 1.1 Fresh Installation
- [ ] Install plugin from WordPress admin
- [ ] Verify no PHP errors during activation
- [ ] Check plugin appears in installed plugins list
- [ ] Verify plugin version matches expected version

### 1.2 Plugin Update
- [ ] Update from previous version
- [ ] Verify settings are preserved
- [ ] Check database migrations complete successfully
- [ ] Confirm no data loss occurs

### 1.3 Deactivation/Reactivation
- [ ] Deactivate plugin
- [ ] Verify no PHP errors
- [ ] Reactivate plugin
- [ ] Confirm settings remain intact

## 2. Settings Configuration

### 2.1 API Settings
- [ ] Configure API credentials (Customer Code, Customer Number, API Key)
- [ ] Test connection to PostNL API
- [ ] Verify error handling for invalid credentials
- [ ] Test sandbox vs production mode switching

### 2.2 Shipping Method Settings
- [ ] Enable/disable PostNL shipping method
- [ ] Configure shipping zones (NL, BE, International)
- [ ] Set up shipping classes and rates
- [ ] Test free shipping thresholds

### 2.3 Checkout Settings
- [ ] Enable/disable delivery days
- [ ] Enable/disable pickup points
- [ ] Configure default checkout tab (Home Delivery/Pickup Points)
- [ ] Test address validation settings
- [ ] Configure delivery options display

### 2.4 Label Settings
- [ ] Configure label format and size
- [ ] Set default product codes
- [ ] Configure return address
- [ ] Test label printer settings

### 2.5 Advanced Settings
- [ ] Configure logging levels
- [ ] Test debug mode
- [ ] Verify HPOS compatibility settings
- [ ] Check performance optimization settings

## 3. Frontend Checkout Testing

### 3.1 Classic Checkout
#### 3.1.1 Netherlands (NL) Customers
**Use Test Data: Jan de Vries (Amsterdam) + Test Product - Standard**
- [ ] Enter valid NL address (Damrak 123A, 1012 JS Amsterdam)
- [ ] Verify postal code validation (1012 JS should be valid)
- [ ] Test house number validation (123 + addition A)
- [ ] Check delivery days display correctly (should show available delivery dates)
- [ ] Verify pickup points load and display (should show nearby PostNL points)
- [ ] Test default tab selection (Home Delivery) - verify "Delivery Days" tab active
- [ ] Test default tab selection (Pickup Points) - verify "Dropoff Points" tab active
- [ ] Verify first option auto-selection in active tab (first delivery/pickup option checked)
- [ ] Test tab switching with auto-selection (switch tabs, verify first option selected)
- [ ] Confirm letterbox delivery options (use Pieter Jansen + Letterbox product)

#### 3.1.2 Belgium (BE) Customers
**Use Test Data: Sophie Dubois (Brussels) + Test Product - Standard**
- [ ] Enter valid BE address (Rue de la Loi 200, 1000 Brussels)
- [ ] Test delivery options display (should show BE-specific options)
- [ ] Verify pickup points for Belgium (should show Belgian PostNL points)
- [ ] Check cross-border shipping rates (verify NL to BE shipping costs)

#### 3.1.3 International Customers
**Use Test Data: Hans Mueller (Berlin) + Test Product - International**
- [ ] Test international shipping options (Hauptstraße 123, 10115 Berlin, Germany)
- [ ] Verify no delivery days/pickup points shown (should only show standard shipping)
- [ ] Check international shipping rates (verify GlobalPack pricing)
- [ ] Test customs declaration fields (HS Code: 9999.99.99, Value: €45.00)

### 3.2 Block-Based Checkout (WooCommerce Blocks)
- [ ] Repeat all classic checkout tests for blocks
- [ ] Verify blocks render correctly
- [ ] Test React component interactions
- [ ] Check mobile responsiveness
- [ ] Verify accessibility compliance

### 3.3 Address Validation
**Use Test Data: Modify NL addresses to test validation**
- [ ] Test with incomplete addresses (e.g., "Damrak" without house number)
- [ ] Verify validation prevents premature API calls (no delivery options until complete)
- [ ] Test address autocomplete functionality (type "1012 JS" should suggest Amsterdam)
- [ ] Check error messages display correctly (clear validation messages shown)

### 3.4 Delivery Options
#### 3.4.1 Delivery Days
- [ ] Verify delivery days load correctly
- [ ] Test different delivery time slots
- [ ] Check evening delivery options
- [ ] Verify morning delivery options
- [ ] Test delivery date selection
- [ ] Check pricing display for premium options

#### 3.4.2 Pickup Points
- [ ] Verify pickup points load based on address
- [ ] Test distance calculation and sorting
- [ ] Check pickup point details (address, opening hours)
- [ ] Verify different pickup point types (PostNL, partners)
- [ ] Test pickup point selection and data storage

### 3.5 Letterbox Delivery
**Use Test Data: Pieter Jansen (Utrecht) + Test Product - Letterbox**
- [ ] Test letterbox-eligible products (dimensions: 23 x 16 x 2 cm, weight: 0.2 kg)
- [ ] Verify letterbox options display (should show letterbox delivery option)
- [ ] Check letterbox delivery restrictions (only for small, light items)
- [ ] Test mixed cart scenarios (add Standard product - letterbox should disappear)

## 4. Order Management (Admin)

### 4.1 Order Processing
**Use Test Data: Create orders with all test customers and products**
- [ ] Create test orders with different delivery options (Jan de Vries + delivery day selection)
- [ ] Verify PostNL data saves correctly to orders (delivery date, pickup point details stored)
- [ ] Check order meta data storage (PostNL fields visible in order admin)
- [ ] Test order status transitions (pending → processing → completed)

### 4.2 Label Generation
- [ ] Generate single labels from order page
- [ ] Test bulk label generation
- [ ] Verify label PDF generation
- [ ] Check label data accuracy
- [ ] Test different label formats (A4, A6)
- [ ] Verify barcode generation

### 4.3 Return Labels
- [ ] Generate return labels
- [ ] Test return label email sending
- [ ] Verify return label PDF format
- [ ] Check return address configuration

### 4.4 Smart Returns
- [ ] Test smart return functionality
- [ ] Verify return tracking
- [ ] Check return processing workflow

### 4.5 Bulk Operations
- [ ] Test bulk label generation
- [ ] Verify bulk status updates
- [ ] Check bulk export functionality
- [ ] Test bulk print operations

## 5. Email Integration

### 5.1 Order Confirmation Emails
- [ ] Verify PostNL delivery info in order emails
- [ ] Test pickup point details in emails
- [ ] Check delivery date information
- [ ] Verify email template customization

### 5.2 Shipping Notification Emails
- [ ] Test shipping confirmation emails
- [ ] Verify tracking information inclusion
- [ ] Check email template rendering
- [ ] Test email sending triggers

### 5.3 Return Label Emails
- [ ] Test return label email generation
- [ ] Verify return instructions
- [ ] Check return label attachment
- [ ] Test email delivery

## 6. API Integration Testing

### 6.1 PostNL Checkout API
- [ ] Test delivery options API calls
- [ ] Verify pickup points API integration
- [ ] Check API error handling
- [ ] Test API rate limiting
- [ ] Verify API response caching

### 6.2 PostNL Shipping API
- [ ] Test label generation API calls
- [ ] Verify tracking API integration
- [ ] Check shipment creation
- [ ] Test API authentication

### 6.3 Error Handling
- [ ] Test API timeout scenarios
- [ ] Verify error message display
- [ ] Check fallback mechanisms
- [ ] Test retry logic

## 7. Performance Testing

### 7.1 Page Load Times
- [ ] Measure checkout page load times
- [ ] Test with multiple delivery options
- [ ] Check JavaScript loading performance
- [ ] Verify CSS loading optimization

### 7.2 API Response Times
- [ ] Measure delivery options API response
- [ ] Test pickup points loading speed
- [ ] Check label generation performance
- [ ] Verify caching effectiveness

### 7.3 Database Performance
- [ ] Test with large order volumes
- [ ] Check database query optimization
- [ ] Verify index usage
- [ ] Test cleanup procedures

## 8. Security Testing

### 8.1 Data Protection
- [ ] Verify API key encryption
- [ ] Test data sanitization
- [ ] Check SQL injection prevention
- [ ] Verify XSS protection

### 8.2 Access Control
- [ ] Test admin capability requirements
- [ ] Verify user permission checks
- [ ] Check AJAX security nonces
- [ ] Test unauthorized access prevention

## 9. Compatibility Testing

### 9.1 WooCommerce Features
- [ ] Test with WooCommerce Subscriptions
- [ ] Verify compatibility with WooCommerce Blocks
- [ ] Check HPOS (High-Performance Order Storage) support
- [ ] Test with WooCommerce REST API

### 9.2 Third-Party Plugins
- [ ] Test with popular checkout plugins
- [ ] Verify compatibility with payment gateways
- [ ] Check integration with shipping plugins
- [ ] Test with multilingual plugins (WPML, Polylang)

### 9.3 Theme Compatibility
- [ ] Test with default WordPress themes
- [ ] Verify with popular WooCommerce themes
- [ ] Check custom theme compatibility
- [ ] Test responsive design

## 10. Internationalization & Localization

### 10.1 Language Support
- [ ] Test Dutch (NL) translations
- [ ] Verify English (EN) text
- [ ] Check German (DE) translations (if supported)
- [ ] Test French (FR) translations (if supported)

### 10.2 Currency Support
- [ ] Test with EUR currency
- [ ] Verify currency formatting
- [ ] Check multi-currency scenarios

### 10.3 Date/Time Formatting
- [ ] Test date formats for different locales
- [ ] Verify time zone handling
- [ ] Check delivery date calculations

## 11. Mobile Testing

### 11.1 Responsive Design
- [ ] Test checkout on mobile devices
- [ ] Verify touch interactions
- [ ] Check mobile-specific UI elements
- [ ] Test landscape/portrait orientations

### 11.2 Mobile Performance
- [ ] Measure mobile page load times
- [ ] Test mobile API performance
- [ ] Check mobile JavaScript execution
- [ ] Verify mobile caching

## 12. Accessibility Testing

### 12.1 WCAG Compliance
- [ ] Test keyboard navigation
- [ ] Verify screen reader compatibility
- [ ] Check color contrast ratios
- [ ] Test focus indicators

### 12.2 Form Accessibility
- [ ] Verify form labels
- [ ] Test error message accessibility
- [ ] Check ARIA attributes
- [ ] Test form validation feedback

## 13. Regression Testing

### 13.1 Core Functionality
- [ ] Re-test all critical user journeys
- [ ] Verify no existing features are broken
- [ ] Check backward compatibility
- [ ] Test data migration scenarios

### 13.2 Bug Fixes
- [ ] Verify previously reported bugs remain fixed
- [ ] Test edge cases that caused issues
- [ ] Check error handling improvements

## 14. Documentation & Support

### 14.1 User Documentation
- [ ] Verify setup instructions accuracy
- [ ] Check feature documentation completeness
- [ ] Test troubleshooting guides
- [ ] Verify FAQ accuracy

### 14.2 Developer Documentation
- [ ] Check API documentation
- [ ] Verify hook/filter documentation
- [ ] Test code examples
- [ ] Check integration guides

## Test Execution Checklist

### Pre-Testing
- [ ] Environment setup complete
- [ ] Test data prepared
- [ ] Browser testing matrix ready
- [ ] Test credentials configured

### During Testing
- [ ] Document all issues found
- [ ] Take screenshots of bugs
- [ ] Record steps to reproduce
- [ ] Note browser/environment details

### Post-Testing
- [ ] Compile test results
- [ ] Create bug reports
- [ ] Verify critical issues resolved
- [ ] Sign off on release readiness

## Critical Path Testing (Minimum Required)

If time is limited, focus on these critical areas:
1. **Plugin activation/deactivation**
2. **Basic checkout flow (NL customers)**
3. **Delivery days and pickup points display**
4. **Label generation**
5. **Order processing**
6. **API connectivity**
7. **Mobile checkout**
8. **WooCommerce Blocks compatibility**

## Success Criteria

The plugin passes QA testing when:
- [ ] All critical path tests pass
- [ ] No blocking bugs identified
- [ ] Performance meets acceptable standards
- [ ] Security vulnerabilities addressed
- [ ] Accessibility requirements met
- [ ] Documentation is accurate and complete

## 15. Edge Cases & Error Scenarios

### 15.1 Network Issues
- [ ] Test with slow internet connection
- [ ] Simulate API timeouts
- [ ] Test offline scenarios
- [ ] Verify graceful degradation

### 15.2 Invalid Data Scenarios
**Use Test Data: Modify valid addresses to create invalid scenarios**
- [ ] Test with malformed addresses (e.g., "Damrak XYZ", invalid house numbers)
- [ ] Enter invalid postal codes (e.g., "0000 XX", "9999 ZZ" for NL)
- [ ] Test with special characters in addresses (e.g., "Straße #@$%")
- [ ] Verify handling of empty responses (simulate API returning no delivery options)

### 15.3 High Load Scenarios
- [ ] Test with multiple concurrent users
- [ ] Simulate high order volumes
- [ ] Test during peak traffic times
- [ ] Verify system stability under load

### 15.4 Data Corruption Scenarios
- [ ] Test with corrupted order data
- [ ] Simulate database connection issues
- [ ] Test plugin recovery mechanisms
- [ ] Verify data integrity checks

## 16. Automated Testing Integration

### 16.1 Unit Tests
- [ ] Run existing PHPUnit tests
- [ ] Verify test coverage
- [ ] Check for new test failures
- [ ] Update tests for new features

### 16.2 Integration Tests
- [ ] Run API integration tests
- [ ] Test database operations
- [ ] Verify external service connections
- [ ] Check end-to-end workflows

### 16.3 Browser Automation
- [ ] Run Selenium/Playwright tests
- [ ] Test critical user journeys
- [ ] Verify cross-browser compatibility
- [ ] Check mobile automation tests

## 17. Monitoring & Analytics

### 17.1 Error Tracking
- [ ] Monitor error logs during testing
- [ ] Check for new error patterns
- [ ] Verify error reporting mechanisms
- [ ] Test error notification systems

### 17.2 Performance Monitoring
- [ ] Monitor page load times
- [ ] Track API response times
- [ ] Check database query performance
- [ ] Verify caching effectiveness

### 17.3 User Experience Metrics
- [ ] Measure checkout completion rates
- [ ] Track user interaction patterns
- [ ] Monitor bounce rates
- [ ] Analyze conversion funnels

## 18. Compliance & Standards

### 18.1 WordPress Standards
- [ ] Verify coding standards compliance
- [ ] Check WordPress hooks usage
- [ ] Test plugin guidelines adherence
- [ ] Verify security best practices

### 18.2 WooCommerce Standards
- [ ] Check WooCommerce coding standards
- [ ] Verify proper use of WC hooks
- [ ] Test template override compatibility
- [ ] Check WC REST API compliance

### 18.3 PostNL API Compliance
- [ ] Verify API usage guidelines
- [ ] Check rate limiting compliance
- [ ] Test error handling requirements
- [ ] Verify data format standards

## Notes

- Execute this test plan for each new WooCommerce version release
- Update test plan as new features are added to the plugin
- Maintain test environment consistency across releases
- Document any new compatibility issues discovered
- Keep test data and scenarios up to date with real-world usage patterns
- Consider automating repetitive test cases to improve efficiency
- Maintain a test environment that mirrors production as closely as possible
- Regular review and update of test cases based on user feedback and bug reports

## Quick Reference - Test Data Summary

### Primary Test Addresses
| Country | Customer | Address | Postal Code | Use Case |
|---------|----------|---------|-------------|----------|
| NL | Jan de Vries | Damrak 123A, Amsterdam | 1012 JS | Standard delivery + pickup points |
| NL | Pieter Jansen | Oudegracht 789bis, Utrecht | 3511 AB | Letterbox delivery |
| BE | Sophie Dubois | Rue de la Loi 200, Brussels | 1000 | Cross-border delivery |
| DE | Hans Mueller | Hauptstraße 123, Berlin | 10115 | International shipping |

### Primary Test Products
| Product | SKU | Weight | Dimensions | Use Case |
|---------|-----|--------|------------|----------|
| Test Product - Standard | TEST-STD-001 | 1.5 kg | 30x20x10 cm | Standard delivery testing |
| Test Product - Letterbox | TEST-LB-001 | 0.2 kg | 23x16x2 cm | Letterbox delivery testing |
| Test Product - International | TEST-INT-001 | 2.0 kg | 35x25x15 cm | International shipping |

### Test Scenarios Quick List
1. **NL Standard**: Jan de Vries + Standard Product = Delivery days + Pickup points
2. **NL Letterbox**: Pieter Jansen + Letterbox Product = Letterbox delivery option
3. **BE Cross-border**: Sophie Dubois + Standard Product = BE delivery options
4. **International**: Hans Mueller + International Product = GlobalPack shipping only
5. **Mixed Cart**: Any NL customer + Standard + Letterbox = Standard delivery only
