Make it possible to decide which tab to show first in the delivery options menu

To further promote out of home delivery we would like to offer merchants the option to decide which of the two delivery options menu tabs (home delivery vs pickup) they would like to show first/by default - this way, merchants have further possibilities to entice their customer to choose the option preferred by the merchant. To do this, a simple button/switch would have to be introduced in the Checkout Settings config section, with which customers can switch between showing the home delivery or pickup delivery tab first.

Important sidenote: based on recent observations into the working of the checkout and delivery options menu I have identified this as a potential risk for causing the bug tackled in this issue :
========
Delivery menu loading too soon when addresschecker is disabled
We were informed on Github that there is an issue with our front-end, which, in some situations, tries to load the delivery options menu before any address details have been entered, resulting in the error "PostalCode required in the request". The person who discovered this issue gave the following steps to reproduce:

Make sure Delivery Days or Pickup points are enabled
Make sure the address validation is turned off
Select The Netherlands as destination in the checkout
select PostNL as shipping method in the checkout
Ensure the postcode field is empty (might have to do this in incognito if the fields are saved from earlier instances)
The error should now appear at the top of the checkout form

The person who raised the issue further comments that the problem does not exist when the address checker is turned on, because then another check is being triggered which prevents the delivery option menu from loading until a postcode has been entered. Perhaps this logic should be adjusted to also trigger without using the address checker

=============

(one which caused calls to our check-out API to be sent out too soon) to resurface again, so please keep that in mind while developing this feature and make sure the aforementioned fix currently awaiting release is not made undone by the development of this feature.


