# Test Plan for Default Checkout Tab Feature

## Overview
This document outlines the test plan for the new "Default Checkout Tab" feature that allows merchants to choose which delivery option tab shows first by default in the checkout.

## Feature Description
- **Setting Location**: WooCommerce > Settings > Shipping > PostNL > Checkout Settings
- **Setting Name**: "Default Checkout Tab"
- **Options**: 
  - Home Delivery (delivery_day)
  - Pickup Points (dropoff_points)
- **Default Value**: Home Delivery

## Test Cases

### 1. Settings Configuration
**Test**: Verify the new setting appears in admin
- [ ] Navigate to WooCommerce > Settings > Shipping > PostNL
- [ ] Scroll to "Checkout Settings" section
- [ ] Verify "Default Checkout Tab" setting is present
- [ ] Verify it has two options: "Home Delivery" and "Pickup Points"
- [ ] Verify default value is "Home Delivery"

### 2. Classic Checkout - Home Delivery Default
**Test**: Verify home delivery tab is selected by default
- [ ] Set "Default Checkout Tab" to "Home Delivery"
- [ ] Go to checkout page with valid NL address
- [ ] Verify "Delivery Days" tab is active by default
- [ ] Verify "Delivery Days" content is visible

### 3. Classic Checkout - Pickup Points Default
**Test**: Verify pickup points tab is selected by default
- [ ] Set "Default Checkout Tab" to "Pickup Points"
- [ ] Go to checkout page with valid NL address
- [ ] Verify "Dropoff Points" tab is active by default
- [ ] Verify "Dropoff Points" content is visible

### 4. Blocks Checkout - Home Delivery Default
**Test**: Verify home delivery tab is selected by default in blocks
- [ ] Set "Default Checkout Tab" to "Home Delivery"
- [ ] Go to blocks checkout page with valid NL address
- [ ] Verify "Delivery Days" tab is active by default
- [ ] Verify "Delivery Days" content is visible

### 5. Blocks Checkout - Pickup Points Default
**Test**: Verify pickup points tab is selected by default in blocks
- [ ] Set "Default Checkout Tab" to "Pickup Points"
- [ ] Go to blocks checkout page with valid NL address
- [ ] Verify "Dropoff Points" tab is active by default
- [ ] Verify "Dropoff Points" content is visible

### 6. Fallback Behavior
**Test**: Verify fallback when preferred tab is not available
- [ ] Set "Default Checkout Tab" to "Pickup Points"
- [ ] Disable pickup points in settings
- [ ] Go to checkout page
- [ ] Verify "Delivery Days" tab is selected (fallback to available tab)

### 7. Postal Code Validation (Regression Test)
**Test**: Ensure postal code validation still works correctly
- [ ] Go to checkout page
- [ ] Leave postcode field empty
- [ ] Verify delivery options don't load prematurely
- [ ] Enter valid postcode
- [ ] Verify delivery options load with correct default tab

### 8. Address Validation (Regression Test)
**Test**: Ensure address validation still works correctly
- [ ] Enable address validation in settings
- [ ] Go to checkout page with NL address
- [ ] Leave house number empty
- [ ] Verify delivery options don't load
- [ ] Enter valid house number
- [ ] Verify delivery options load with correct default tab

## Expected Results
- Setting should be saved correctly in database
- Default tab selection should work in both classic and blocks checkout
- Postal code and address validation should continue to work as before
- No JavaScript errors should occur
- Tab switching should work normally after initial load

## Browser Testing
Test in the following browsers:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

## Notes
- This feature only affects which tab is selected by default
- It does not change the validation logic or when delivery options are loaded
- The feature respects the availability of tabs (e.g., if pickup points are disabled)
